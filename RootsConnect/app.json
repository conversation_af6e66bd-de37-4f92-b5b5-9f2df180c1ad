{"expo": {"name": "RootsConnect", "slug": "rootsconnect", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#FF6B35"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.rootsconnect.app"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FF6B35"}, "package": "com.rootsconnect.app", "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-location"], "scheme": "rootsconnect"}}