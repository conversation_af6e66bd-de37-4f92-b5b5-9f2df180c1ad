/**
 * UserCard component for displaying user information
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { MapUser } from '../types';

interface UserCardProps {
  user: MapUser;
  onPress?: () => void;
  onConnect?: () => void;
  showDistance?: boolean;
  showConnectButton?: boolean;
}

const UserCard: React.FC<UserCardProps> = ({
  user,
  onPress,
  onConnect,
  showDistance = true,
  showConnectButton = true,
}) => {
  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <View style={styles.content}>
        {/* Profile Image */}
        <View style={styles.imageContainer}>
          {user.profilePicture ? (
            <Image source={{ uri: user.profilePicture }} style={styles.profileImage} />
          ) : (
            <View style={styles.placeholderImage}>
              <Icon name="person" size={32} color="#666" />
            </View>
          )}
          {user.willingToMeet && (
            <View style={styles.statusIndicator}>
              <Icon name="location-on" size={12} color="#fff" />
            </View>
          )}
        </View>

        {/* User Info */}
        <View style={styles.userInfo}>
          <Text style={styles.displayName} numberOfLines={1}>
            {user.displayName}
          </Text>
          {showDistance && user.distance !== undefined && (
            <View style={styles.distanceContainer}>
              <Icon name="location-on" size={14} color="#666" />
              <Text style={styles.distanceText}>
                {user.distance < 1 
                  ? `${Math.round(user.distance * 1000)}m away`
                  : `${user.distance.toFixed(1)}km away`
                }
              </Text>
            </View>
          )}
          <View style={styles.statusContainer}>
            <View style={[styles.statusDot, { backgroundColor: user.willingToMeet ? '#4CAF50' : '#FFC107' }]} />
            <Text style={styles.statusText}>
              {user.willingToMeet ? 'Willing to meet' : 'Online only'}
            </Text>
          </View>
        </View>

        {/* Connect Button */}
        {showConnectButton && (
          <TouchableOpacity style={styles.connectButton} onPress={onConnect}>
            <Icon name="person-add" size={20} color="#FF6B35" />
          </TouchableOpacity>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  imageContainer: {
    position: 'relative',
    marginRight: 16,
  },
  profileImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#f0f0f0',
  },
  placeholderImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusIndicator: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: '#4CAF50',
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#fff',
  },
  userInfo: {
    flex: 1,
  },
  displayName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  distanceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  distanceText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 4,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 12,
    color: '#666',
  },
  connectButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#fff',
    borderWidth: 2,
    borderColor: '#FF6B35',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default UserCard;
