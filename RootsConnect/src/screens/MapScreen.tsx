import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  TouchableOpacity,
  SafeAreaView,
  FlatList,
  Modal,
} from 'react-native';
import MapView, { Marker, PROVIDER_GOOGLE } from 'react-native-maps';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useAuthenticator } from '@aws-amplify/ui-react-native';
import { LocationService } from '../services/locationService';
import { DataService } from '../services/dataService';
import { MapUser } from '../types';
import UserCard from '../components/UserCard';

interface UserLocation {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}

const MapScreen: React.FC = () => {
  const { user } = useAuthenticator();
  const [userLocation, setUserLocation] = useState<UserLocation | null>(null);
  const [mapType, setMapType] = useState<'standard' | 'satellite'>('standard');
  const [nearbyUsers, setNearbyUsers] = useState<MapUser[]>([]);
  const [loading, setLoading] = useState(false);
  const [showUserList, setShowUserList] = useState(false);
  const [selectedUser, setSelectedUser] = useState<MapUser | null>(null);

  useEffect(() => {
    loadUserLocation();
  }, []);

  const loadUserLocation = async () => {
    try {
      setLoading(true);
      const location = await LocationService.getLocationWithPermission();

      if (location) {
        const newUserLocation = {
          latitude: location.latitude,
          longitude: location.longitude,
          latitudeDelta: 0.0922,
          longitudeDelta: 0.0421,
        };
        setUserLocation(newUserLocation);

        // Load nearby users
        await loadNearbyUsers(location.latitude, location.longitude);
      } else {
        // Fallback to default location
        setUserLocation({
          latitude: 37.7749, // San Francisco
          longitude: -122.4194,
          latitudeDelta: 0.0922,
          longitudeDelta: 0.0421,
        });
      }
    } catch (error) {
      console.error('Error loading user location:', error);
      Alert.alert('Error', 'Unable to load your location. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const loadNearbyUsers = async (latitude: number, longitude: number) => {
    try {
      const users = await DataService.getNearbyUsers(latitude, longitude, 50);
      setNearbyUsers(users);
    } catch (error) {
      console.error('Error loading nearby users:', error);
    }
  };

  const handleLocationRequest = () => {
    loadUserLocation();
  };

  const handleConnectUser = async (targetUser: MapUser) => {
    try {
      if (!user?.userId) {
        Alert.alert('Error', 'Please sign in to connect with other users.');
        return;
      }

      await DataService.sendConnectionRequest(
        user.userId,
        targetUser.id,
        `Hi! I'd like to connect with you through RootsConnect.`
      );

      Alert.alert(
        'Connection Request Sent',
        `Your connection request has been sent to ${targetUser.displayName}.`
      );
    } catch (error) {
      console.error('Error sending connection request:', error);
      Alert.alert('Error', 'Unable to send connection request. Please try again.');
    }
  };

  const toggleMapType = () => {
    setMapType(mapType === 'standard' ? 'satellite' : 'standard');
  };

  if (!userLocation) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Icon name="location-searching" size={48} color="#FF6B35" />
          <Text style={styles.loadingText}>Loading map...</Text>
          <TouchableOpacity style={styles.locationButton} onPress={handleLocationRequest}>
            <Text style={styles.locationButtonText}>Enable Location</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <MapView
        provider={PROVIDER_GOOGLE}
        style={styles.map}
        region={userLocation}
        mapType={mapType}
        showsUserLocation={true}
        showsMyLocationButton={true}
      >
        {/* Sample markers for demonstration */}
        <Marker
          coordinate={{
            latitude: userLocation.latitude + 0.01,
            longitude: userLocation.longitude + 0.01,
          }}
          title="Community Member"
          description="Tap to connect"
        />
        <Marker
          coordinate={{
            latitude: userLocation.latitude - 0.01,
            longitude: userLocation.longitude - 0.01,
          }}
          title="Another Member"
          description="Tap to connect"
        />
      </MapView>

      {/* Map Controls */}
      <View style={styles.controls}>
        <TouchableOpacity style={styles.controlButton} onPress={toggleMapType}>
          <Icon name="layers" size={24} color="#333" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.controlButton} onPress={handleLocationRequest}>
          <Icon name="my-location" size={24} color="#333" />
        </TouchableOpacity>
      </View>

      {/* Info Panel */}
      <View style={styles.infoPanel}>
        <Text style={styles.infoPanelTitle}>Discover Nearby</Text>
        <Text style={styles.infoPanelText}>
          Find Indian diaspora community members in your area. 
          Enable location sharing to connect with others.
        </Text>
        <TouchableOpacity style={styles.privacyButton}>
          <Icon name="security" size={16} color="#FF6B35" />
          <Text style={styles.privacyButtonText}>Privacy Settings</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    fontSize: 18,
    color: '#333',
    marginTop: 16,
    marginBottom: 24,
  },
  locationButton: {
    backgroundColor: '#FF6B35',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  locationButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  map: {
    flex: 1,
  },
  controls: {
    position: 'absolute',
    top: 60,
    right: 16,
    gap: 12,
  },
  controlButton: {
    backgroundColor: '#fff',
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  infoPanel: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#fff',
    padding: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  infoPanelTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  infoPanelText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 16,
  },
  privacyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
  },
  privacyButtonText: {
    color: '#FF6B35',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
});

export default MapScreen;
