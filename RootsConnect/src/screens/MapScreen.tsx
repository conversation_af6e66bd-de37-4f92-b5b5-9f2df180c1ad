import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  TouchableOpacity,
  SafeAreaView,
  FlatList,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useAuthenticator } from '@aws-amplify/ui-react-native';
import { LocationService } from '../services/locationService';
import { DataService } from '../services/dataService';
import { MapUser } from '../types';
import UserCard from '../components/UserCard';

interface UserLocation {
  latitude: number;
  longitude: number;
  city?: string;
  state?: string;
  country?: string;
}

const MapScreen: React.FC = () => {
  const { user } = useAuthenticator();
  const [userLocation, setUserLocation] = useState<UserLocation | null>(null);
  const [nearbyUsers, setNearbyUsers] = useState<MapUser[]>([]);
  const [loading, setLoading] = useState(false);
  const [locationError, setLocationError] = useState<string | null>(null);

  useEffect(() => {
    loadUserLocation();
  }, []);

  const loadUserLocation = async () => {
    try {
      setLoading(true);
      setLocationError(null);

      const location = await LocationService.getLocationWithPermission();

      if (location) {
        const newUserLocation = {
          latitude: location.latitude,
          longitude: location.longitude,
          city: 'Current Location',
        };
        setUserLocation(newUserLocation);

        // Load nearby users (mock data for now)
        await loadNearbyUsers(location.latitude, location.longitude);
      } else {
        setLocationError('Location access denied. Enable location to discover nearby members.');
        // Load mock data for demo
        await loadMockUsers();
      }
    } catch (error) {
      console.error('Error loading user location:', error);
      setLocationError('Unable to load your location. Showing demo data.');
      await loadMockUsers();
    } finally {
      setLoading(false);
    }
  };

  const loadNearbyUsers = async (latitude: number, longitude: number) => {
    try {
      // For now, load mock data since backend might not have users yet
      await loadMockUsers();
    } catch (error) {
      console.error('Error loading nearby users:', error);
      await loadMockUsers();
    }
  };

  const loadMockUsers = async () => {
    // Mock data for demonstration
    const mockUsers: MapUser[] = [
      {
        id: '1',
        displayName: 'Priya Sharma',
        latitude: 37.7849,
        longitude: -122.4094,
        willingToMeet: true,
        distance: 0.8,
        profilePicture: 'https://via.placeholder.com/100',
      },
      {
        id: '2',
        displayName: 'Raj Patel',
        latitude: 37.7649,
        longitude: -122.4294,
        willingToMeet: true,
        distance: 1.2,
      },
      {
        id: '3',
        displayName: 'Anita Kumar',
        latitude: 37.7949,
        longitude: -122.3994,
        willingToMeet: false,
        distance: 2.1,
      },
    ];
    setNearbyUsers(mockUsers);
  };

  const handleLocationRequest = () => {
    loadUserLocation();
  };

  const handleConnectUser = async (targetUser: MapUser) => {
    try {
      if (!user?.userId) {
        Alert.alert('Error', 'Please sign in to connect with other users.');
        return;
      }

      // For demo purposes, just show success message
      Alert.alert(
        'Connection Request Sent',
        `Your connection request has been sent to ${targetUser.displayName}. This is a demo - backend integration coming soon!`
      );
    } catch (error) {
      console.error('Error sending connection request:', error);
      Alert.alert('Error', 'Unable to send connection request. Please try again.');
    }
  };

  const renderUserItem = ({ item }: { item: MapUser }) => (
    <UserCard
      user={item}
      onPress={() => {
        Alert.alert(
          item.displayName,
          `Distance: ${item.distance}km\nStatus: ${item.willingToMeet ? 'Willing to meet' : 'Online only'}`,
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Connect', onPress: () => handleConnectUser(item) },
          ]
        );
      }}
      onConnect={() => handleConnectUser(item)}
      showDistance={true}
      showConnectButton={true}
    />
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF6B35" />
          <Text style={styles.loadingText}>Finding nearby community members...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Icon name="location-on" size={24} color="#FF6B35" />
          <View style={styles.locationInfo}>
            <Text style={styles.headerTitle}>Discover Nearby</Text>
            <Text style={styles.headerSubtitle}>
              {userLocation
                ? `${userLocation.city || 'Current Location'}`
                : 'Location not available'
              }
            </Text>
          </View>
        </View>
        <TouchableOpacity style={styles.refreshButton} onPress={handleLocationRequest}>
          <Icon name="refresh" size={24} color="#FF6B35" />
        </TouchableOpacity>
      </View>

      {/* Location Error */}
      {locationError && (
        <View style={styles.errorContainer}>
          <Icon name="warning" size={20} color="#FF9800" />
          <Text style={styles.errorText}>{locationError}</Text>
        </View>
      )}

      {/* Stats */}
      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{nearbyUsers.length}</Text>
          <Text style={styles.statLabel}>Nearby Members</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>
            {nearbyUsers.filter(u => u.willingToMeet).length}
          </Text>
          <Text style={styles.statLabel}>Willing to Meet</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>
            {nearbyUsers.filter(u => u.distance && u.distance < 5).length}
          </Text>
          <Text style={styles.statLabel}>Within 5km</Text>
        </View>
      </View>

      {/* User List */}
      <FlatList
        data={nearbyUsers}
        renderItem={renderUserItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Icon name="people-outline" size={64} color="#ccc" />
            <Text style={styles.emptyTitle}>No members found nearby</Text>
            <Text style={styles.emptyText}>
              Be the first to join the community in your area!
            </Text>
            <TouchableOpacity style={styles.inviteButton}>
              <Text style={styles.inviteButtonText}>Invite Friends</Text>
            </TouchableOpacity>
          </View>
        }
      />

      {/* Privacy Notice */}
      <View style={styles.privacyNotice}>
        <Icon name="security" size={16} color="#666" />
        <Text style={styles.privacyText}>
          Your location is private. Only members you connect with can see your details.
        </Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    fontSize: 16,
    color: '#333',
    marginTop: 16,
    textAlign: 'center',
  },
  header: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  locationInfo: {
    marginLeft: 12,
    flex: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  refreshButton: {
    padding: 8,
  },
  errorContainer: {
    backgroundColor: '#FFF3E0',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#FF9800',
  },
  errorText: {
    fontSize: 14,
    color: '#E65100',
    marginLeft: 8,
    flex: 1,
  },
  statsContainer: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 20,
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FF6B35',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
    textAlign: 'center',
  },
  listContainer: {
    paddingTop: 16,
    paddingBottom: 100,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  inviteButton: {
    backgroundColor: '#FF6B35',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  inviteButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  privacyNotice: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#fff',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  privacyText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 8,
    flex: 1,
  },
});

export default MapScreen;
