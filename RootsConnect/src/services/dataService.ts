/**
 * Data service for RootsConnect app
 * Handles all interactions with AWS Amplify backend
 */

import { generateClient } from 'aws-amplify/data';
import type { Schema } from '../../amplify/data/resource';
import { UserProfile, ConnectionRequest, Connection, MapUser } from '../types';

// Generate the Amplify client
const client = generateClient<Schema>();

export class DataService {
  /**
   * User Profile Operations
   */
  
  // Create or update user profile
  static async createUserProfile(profileData: Partial<UserProfile>) {
    try {
      const result = await client.models.UserProfile.create({
        userId: profileData.userId!,
        email: profileData.email!,
        firstName: profileData.firstName,
        lastName: profileData.lastName,
        displayName: profileData.displayName,
        profilePicture: profileData.profilePicture,
        latitude: profileData.latitude,
        longitude: profileData.longitude,
        city: profileData.city,
        state: profileData.state,
        country: profileData.country,
        isLocationVisible: profileData.isLocationVisible || false,
        phoneNumbers: profileData.phoneNumbers,
        whatsappNumber: profileData.whatsappNumber,
        facebookProfile: profileData.facebookProfile,
        telegramHandle: profileData.telegramHandle,
        additionalEmails: profileData.additionalEmails,
        isProfilePublic: profileData.isProfilePublic || false,
        shareContactInfo: profileData.shareContactInfo || false,
        allowDirectContact: profileData.allowDirectContact || false,
        interests: profileData.interests,
        profession: profileData.profession,
        bio: profileData.bio,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        lastActiveAt: new Date().toISOString(),
      });
      return result.data;
    } catch (error) {
      console.error('Error creating user profile:', error);
      throw error;
    }
  }

  // Get user profile by userId
  static async getUserProfile(userId: string) {
    try {
      const result = await client.models.UserProfile.list({
        filter: { userId: { eq: userId } }
      });
      return result.data[0] || null;
    } catch (error) {
      console.error('Error fetching user profile:', error);
      throw error;
    }
  }

  // Update user profile
  static async updateUserProfile(profileId: string, updates: Partial<UserProfile>) {
    try {
      const result = await client.models.UserProfile.update({
        id: profileId,
        ...updates,
        updatedAt: new Date().toISOString(),
      });
      return result.data;
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  }

  // Get nearby users for map display
  static async getNearbyUsers(latitude: number, longitude: number, radiusKm: number = 50): Promise<MapUser[]> {
    try {
      // Note: This is a simplified version. In production, you'd want to use geospatial queries
      const result = await client.models.UserProfile.list({
        filter: {
          isLocationVisible: { eq: true },
          isProfilePublic: { eq: true }
        }
      });

      // Filter by distance (simplified calculation)
      const nearbyUsers = result.data
        .filter(user => user.latitude && user.longitude)
        .map(user => {
          const distance = this.calculateDistance(
            latitude, longitude,
            user.latitude!, user.longitude!
          );
          return {
            id: user.id,
            displayName: user.displayName || `${user.firstName} ${user.lastName}`.trim() || 'Anonymous',
            latitude: user.latitude!,
            longitude: user.longitude!,
            profilePicture: user.profilePicture,
            willingToMeet: true, // Assuming users visible on map are willing to meet
            distance
          };
        })
        .filter(user => user.distance <= radiusKm)
        .sort((a, b) => a.distance - b.distance);

      return nearbyUsers;
    } catch (error) {
      console.error('Error fetching nearby users:', error);
      throw error;
    }
  }

  /**
   * Connection Operations
   */

  // Send connection request
  static async sendConnectionRequest(fromUserId: string, toUserId: string, message?: string) {
    try {
      const result = await client.models.ConnectionRequest.create({
        fromUserId,
        toUserId,
        status: 'pending',
        message,
        createdAt: new Date().toISOString(),
      });
      return result.data;
    } catch (error) {
      console.error('Error sending connection request:', error);
      throw error;
    }
  }

  // Get connection requests for a user
  static async getConnectionRequests(userId: string) {
    try {
      const result = await client.models.ConnectionRequest.list({
        filter: { toUserId: { eq: userId } }
      });
      return result.data;
    } catch (error) {
      console.error('Error fetching connection requests:', error);
      throw error;
    }
  }

  // Accept/decline connection request
  static async updateConnectionRequest(requestId: string, status: 'accepted' | 'declined') {
    try {
      const result = await client.models.ConnectionRequest.update({
        id: requestId,
        status,
        updatedAt: new Date().toISOString(),
      });

      // If accepted, create a connection
      if (status === 'accepted' && result.data) {
        await this.createConnection(result.data.fromUserId, result.data.toUserId);
      }

      return result.data;
    } catch (error) {
      console.error('Error updating connection request:', error);
      throw error;
    }
  }

  // Create connection between two users
  static async createConnection(user1Id: string, user2Id: string) {
    try {
      const result = await client.models.Connection.create({
        user1Id,
        user2Id,
        connectedAt: new Date().toISOString(),
        sharedContactInfo: false,
      });
      return result.data;
    } catch (error) {
      console.error('Error creating connection:', error);
      throw error;
    }
  }

  // Get user's connections
  static async getUserConnections(userId: string) {
    try {
      const result = await client.models.Connection.list({
        filter: {
          or: [
            { user1Id: { eq: userId } },
            { user2Id: { eq: userId } }
          ]
        }
      });
      return result.data;
    } catch (error) {
      console.error('Error fetching user connections:', error);
      throw error;
    }
  }

  /**
   * Utility Functions
   */

  // Calculate distance between two coordinates (Haversine formula)
  private static calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  private static toRadians(degrees: number): number {
    return degrees * (Math.PI/180);
  }
}
