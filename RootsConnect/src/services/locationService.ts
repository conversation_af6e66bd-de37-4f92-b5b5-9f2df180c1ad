/**
 * Location service for RootsConnect app
 * Handles geolocation and location-related operations
 */

import { PermissionsAndroid, Platform, Alert } from 'react-native';

export interface LocationCoordinates {
  latitude: number;
  longitude: number;
  accuracy?: number;
}

export interface LocationError {
  code: number;
  message: string;
}

export class LocationService {
  /**
   * Request location permissions
   */
  static async requestLocationPermission(): Promise<boolean> {
    try {
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          {
            title: 'RootsConnect Location Permission',
            message: 'RootsConnect needs access to your location to help you discover nearby community members.',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          }
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } else {
        // iOS permissions are handled automatically by the system
        return true;
      }
    } catch (error) {
      console.error('Error requesting location permission:', error);
      return false;
    }
  }

  /**
   * Get current location
   */
  static async getCurrentLocation(): Promise<LocationCoordinates> {
    return new Promise((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          resolve({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
          });
        },
        (error) => {
          console.error('Error getting current location:', error);
          reject({
            code: error.code,
            message: error.message,
          });
        },
        {
          enableHighAccuracy: true,
          timeout: 15000,
          maximumAge: 10000,
        }
      );
    });
  }

  /**
   * Watch location changes
   */
  static watchLocation(
    onLocationUpdate: (location: LocationCoordinates) => void,
    onError?: (error: LocationError) => void
  ): number {
    return navigator.geolocation.watchPosition(
      (position) => {
        onLocationUpdate({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
        });
      },
      (error) => {
        console.error('Error watching location:', error);
        if (onError) {
          onError({
            code: error.code,
            message: error.message,
          });
        }
      },
      {
        enableHighAccuracy: true,
        timeout: 30000,
        maximumAge: 10000,
      }
    );
  }

  /**
   * Stop watching location
   */
  static stopWatchingLocation(watchId: number): void {
    navigator.geolocation.clearWatch(watchId);
  }

  /**
   * Get location with user-friendly error handling
   */
  static async getLocationWithPermission(): Promise<LocationCoordinates | null> {
    try {
      // First check if we have permission
      const hasPermission = await this.requestLocationPermission();
      
      if (!hasPermission) {
        Alert.alert(
          'Location Permission Required',
          'To discover nearby community members, please enable location access in your device settings.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Settings', onPress: () => {
              // In a real app, you'd open device settings here
              console.log('Open device settings');
            }},
          ]
        );
        return null;
      }

      // Get current location
      const location = await this.getCurrentLocation();
      return location;
    } catch (error: any) {
      let errorMessage = 'Unable to get your location.';
      
      switch (error.code) {
        case 1: // PERMISSION_DENIED
          errorMessage = 'Location access was denied. Please enable location services.';
          break;
        case 2: // POSITION_UNAVAILABLE
          errorMessage = 'Location information is unavailable.';
          break;
        case 3: // TIMEOUT
          errorMessage = 'Location request timed out. Please try again.';
          break;
        default:
          errorMessage = error.message || 'An unknown error occurred while getting your location.';
      }

      Alert.alert('Location Error', errorMessage);
      return null;
    }
  }

  /**
   * Calculate distance between two points (Haversine formula)
   */
  static calculateDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  /**
   * Format distance for display
   */
  static formatDistance(distanceKm: number): string {
    if (distanceKm < 1) {
      return `${Math.round(distanceKm * 1000)}m away`;
    } else if (distanceKm < 10) {
      return `${distanceKm.toFixed(1)}km away`;
    } else {
      return `${Math.round(distanceKm)}km away`;
    }
  }

  /**
   * Convert degrees to radians
   */
  private static toRadians(degrees: number): number {
    return degrees * (Math.PI/180);
  }

  /**
   * Reverse geocoding (get address from coordinates)
   * Note: This would typically use a service like Google Maps Geocoding API
   */
  static async reverseGeocode(latitude: number, longitude: number): Promise<string> {
    try {
      // This is a placeholder - in a real app, you'd use a geocoding service
      return `${latitude.toFixed(4)}, ${longitude.toFixed(4)}`;
    } catch (error) {
      console.error('Error reverse geocoding:', error);
      return 'Unknown location';
    }
  }
}
