/**
 * Type definitions for RootsConnect app
 */

export interface UserProfile {
  id: string;
  userId: string;
  email: string;
  firstName?: string;
  lastName?: string;
  displayName?: string;
  profilePicture?: string;
  
  // Location information
  latitude?: number;
  longitude?: number;
  city?: string;
  state?: string;
  country?: string;
  isLocationVisible: boolean;
  
  // Contact information
  phoneNumbers?: string[];
  whatsappNumber?: string;
  facebookProfile?: string;
  telegramHandle?: string;
  additionalEmails?: string[];
  
  // Privacy settings
  isProfilePublic: boolean;
  shareContactInfo: boolean;
  allowDirectContact: boolean;
  willingToMeet?: boolean;
  
  // Community information
  interests?: string[];
  profession?: string;
  bio?: string;
  
  // Metadata
  createdAt?: string;
  updatedAt?: string;
  lastActiveAt?: string;
}

export interface ConnectionRequest {
  id: string;
  fromUserId: string;
  toUserId: string;
  status: 'pending' | 'accepted' | 'declined';
  message?: string;
  createdAt: string;
  updatedAt?: string;
}

export interface Connection {
  id: string;
  user1Id: string;
  user2Id: string;
  connectedAt: string;
  sharedContactInfo: boolean;
}

export interface MapUser {
  id: string;
  displayName: string;
  latitude: number;
  longitude: number;
  profilePicture?: string;
  willingToMeet: boolean;
  distance?: number;
}

export interface ContactInfo {
  phoneNumbers?: string[];
  whatsappNumber?: string;
  facebookProfile?: string;
  telegramHandle?: string;
  additionalEmails?: string[];
}

export interface PrivacySettings {
  isProfilePublic: boolean;
  isLocationVisible: boolean;
  shareContactInfo: boolean;
  allowDirectContact: boolean;
  willingToMeet: boolean;
}

export interface NavigationProps {
  navigation: any;
  route: any;
}
