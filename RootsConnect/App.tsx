/**
 * RootsConnect - Indian Diaspora Community Discovery Platform
 * Main App Component with AWS Amplify Authentication
 */

import React, { useEffect } from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  useColorScheme,
} from 'react-native';
import { Amplify } from 'aws-amplify';
import { Authenticator } from '@aws-amplify/ui-react-native';
import { NavigationContainer } from '@react-navigation/native';

// Import Amplify configuration
import './src/amplifyconfiguration';
import MainNavigator from './src/navigation/MainNavigator';

function App(): React.JSX.Element {
  const isDarkMode = useColorScheme() === 'dark';

  useEffect(() => {
    // Configure Amplify on app start
    console.log('RootsConnect app initialized');
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor={isDarkMode ? '#000' : '#fff'}
      />
      <Authenticator.Provider>
        <Authenticator
          // Customize the authenticator
          loginMechanisms={['email']}
          signUpAttributes={['email', 'given_name', 'family_name']}
          socialProviders={['google', 'facebook']}
        >
          <NavigationContainer>
            <MainNavigator />
          </NavigationContainer>
        </Authenticator>
      </Authenticator.Provider>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default App;
