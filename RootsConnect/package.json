{"name": "RootsConnect", "version": "0.0.1", "private": true, "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "eslint .", "test": "jest"}, "dependencies": {"@aws-amplify/ui-react-native": "^2.5.3", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/geolocation": "^3.4.0", "@react-native/new-app-screen": "0.80.0", "@react-navigation/bottom-tabs": "^7.4.0", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.0", "aws-amplify": "^6.15.1", "expo": "~53.0.0", "expo-constants": "^17.1.6", "expo-location": "^18.1.5", "react": "18.2.0", "react-native": "0.74.5", "react-native-gesture-handler": "~2.16.1", "react-native-maps": "1.14.0", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.31.1", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"@aws-amplify/backend": "^1.16.1", "@aws-amplify/backend-cli": "^1.8.0", "@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "19.0.0", "@react-native-community/cli-platform-android": "19.0.0", "@react-native-community/cli-platform-ios": "19.0.0", "@react-native/babel-preset": "0.80.0", "@react-native/eslint-config": "0.80.0", "@react-native/metro-config": "0.80.0", "@react-native/typescript-config": "0.80.0", "@types/jest": "^29.5.13", "@types/react": "^19.1.0", "@types/react-test-renderer": "^19.1.0", "aws-cdk-lib": "^2.189.1", "constructs": "^10.4.2", "esbuild": "^0.25.5", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.1.0", "tsx": "^4.20.3", "typescript": "^5.8.3"}, "engines": {"node": ">=18"}}