import { type ClientSchema, a, defineData } from '@aws-amplify/backend';

/**
 * RootsConnect Data Schema
 * Defines the data models for the Indian diaspora community discovery platform
 */
const schema = a.schema({
  UserProfile: a
    .model({
      // Basic user information
      userId: a.string().required(),
      email: a.email().required(),
      firstName: a.string(),
      lastName: a.string(),
      displayName: a.string(),
      profilePicture: a.url(),

      // Location information
      latitude: a.float(),
      longitude: a.float(),
      city: a.string(),
      state: a.string(),
      country: a.string(),
      isLocationVisible: a.boolean().default(false),

      // Contact information (privacy-controlled)
      phoneNumbers: a.string().array(),
      whatsappNumber: a.string(),
      facebookProfile: a.url(),
      telegramHandle: a.string(),
      additionalEmails: a.email().array(),

      // Privacy settings
      isProfilePublic: a.boolean().default(false),
      shareContactInfo: a.boolean().default(false),
      allowDirectContact: a.boolean().default(false),

      // Community information
      interests: a.string().array(),
      profession: a.string(),
      bio: a.string(),

      // Metadata
      createdAt: a.datetime(),
      updatedAt: a.datetime(),
      lastActiveAt: a.datetime(),
    })
    .authorization((allow) => [
      allow.owner(),
      allow.authenticated().to(['read']),
    ]),

  ConnectionRequest: a
    .model({
      fromUserId: a.string().required(),
      toUserId: a.string().required(),
      status: a.enum(['pending', 'accepted', 'declined']),
      message: a.string(),
      createdAt: a.datetime(),
      updatedAt: a.datetime(),
    })
    .authorization((allow) => [
      allow.owner().to(['create', 'read', 'update']),
      allow.authenticated().to(['read']),
    ]),

  Connection: a
    .model({
      user1Id: a.string().required(),
      user2Id: a.string().required(),
      connectedAt: a.datetime(),
      sharedContactInfo: a.boolean().default(false),
    })
    .authorization((allow) => [
      allow.authenticated().to(['read']),
    ]),
});

export type Schema = ClientSchema<typeof schema>;

export const data = defineData({
  schema,
  authorizationModes: {
    defaultAuthorizationMode: 'userPool',
    apiKeyAuthorizationMode: {
      expiresInDays: 30,
    },
  },
});
