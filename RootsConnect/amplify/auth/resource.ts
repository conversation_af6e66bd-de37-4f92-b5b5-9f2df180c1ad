import { defineAuth } from '@aws-amplify/backend';

/**
 * Define and configure your auth resource
 * @see https://docs.amplify.aws/gen2/build-a-backend/auth
 */
export const auth = defineAuth({
  loginWith: {
    email: true,
    // Note: Social providers will be configured later via console
    // externalProviders: {
    //   google: {
    //     clientId: secret('GOOGLE_CLIENT_ID'),
    //     clientSecret: secret('GOOGLE_CLIENT_SECRET'),
    //   },
    //   facebook: {
    //     clientId: secret('FACEBOOK_CLIENT_ID'),
    //     clientSecret: secret('FACEBOOK_CLIENT_SECRET'),
    //   },
    //   callbackUrls: [
    //     'http://localhost:3000/',
    //     'https://main.d1234567890.amplifyapp.com/',
    //   ],
    //   logoutUrls: [
    //     'http://localhost:3000/',
    //     'https://main.d1234567890.amplifyapp.com/',
    //   ],
    // },
  },
  userAttributes: {
    email: {
      required: true,
      mutable: true,
    },
    givenName: {
      required: false,
      mutable: true,
    },
    familyName: {
      required: false,
      mutable: true,
    },
    phoneNumber: {
      required: false,
      mutable: true,
    },
  },
});
